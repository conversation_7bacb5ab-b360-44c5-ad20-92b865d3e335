import ccxt
import pandas as pd
import numpy as np
import ta
from datetime import datetime
import time
import statistics
import concurrent.futures
from tabulate import tabulate

class CryptoTradingBot:
    def __init__(self, exchange_id='bybit', timeframe='1h', scan_interval=3600, top_n=10, auto_scan=True, specific_symbol=None,
                 daily_target_usd=20, max_leverage=5, trading_fee_pct=0.1):
        self.exchange = getattr(ccxt, exchange_id)({
            'enableRateLimit': True,
            'sandbox': False,  # Set to True for testing
        })
        self.timeframe = timeframe
        self.scan_interval = scan_interval
        self.top_n = top_n  # Used for top coins in auto_scan mode
        self.auto_scan = auto_scan
        self.specific_symbol = specific_symbol  # Used when auto_scan is False

        # Position sizing parameters
        self.daily_target_usd = daily_target_usd  # Target daily profit in USD
        self.max_leverage = max_leverage  # Maximum leverage to use
        self.trading_fee_pct = trading_fee_pct  # Trading fee percentage (0.1% = 0.1)
        self.trades_per_day = 4  # Expected number of trades per day

        self.signal_history = {}
        self.win_rate_threshold = 60  # Minimum win rate for high probability signals (%)
        self.top_coins = []
        self.last_scan_time = 0
        self.max_coins_to_scan = 50  # Max coins to scan in auto_scan mode

    def get_usdt_pairs(self, exchange_type='future'):
        """Get all available USDT trading pairs from Bybit"""
        try:
            print("🔄 Đang tải danh sách markets từ Bybit...")
            markets = self.exchange.load_markets()
            print(f"✅ Đã tải {len(markets)} markets")

            usdt_pairs = []

            # Debug: Print some market info
            sample_markets = list(markets.keys())[:5]
            print(f"🔍 Ví dụ markets: {sample_markets}")

            # Debug: Print market structure for first few USDT pairs
            debug_count = 0
            for symbol in markets.keys():
                market = markets[symbol]
                if symbol.endswith('/USDT') and debug_count < 3:
                    print(f"   Debug {symbol}: {market}")
                    debug_count += 1

            for symbol in markets.keys():
                market = markets[symbol]
                if symbol.endswith('/USDT'):
                    # For Bybit, let's be more flexible with filtering
                    if exchange_type == 'future':
                        # Try different ways to identify futures
                        if (market.get('type') in ['swap', 'future', 'linear'] or
                            market.get('linear', False) or
                            market.get('swap', False) or
                            'linear' in str(market).lower() or
                            'perpetual' in str(market).lower()):
                            usdt_pairs.append(symbol)
                            if len(usdt_pairs) <= 5:  # Debug first few
                                print(f"   Tìm thấy Futures: {symbol} - Type: {market.get('type', 'unknown')}")
                    else:  # spot
                        if (market.get('spot', False) or
                            market.get('type') == 'spot'):
                            usdt_pairs.append(symbol)
                            if len(usdt_pairs) <= 5:  # Debug first few
                                print(f"   Tìm thấy Spot: {symbol} - Type: {market.get('type', 'unknown')}")

                    # If still no pairs found, add all USDT pairs for debugging
                    if len(usdt_pairs) == 0 and symbol.endswith('/USDT'):
                        usdt_pairs.append(symbol)
                        if len(usdt_pairs) <= 10:
                            print(f"   Fallback: {symbol} - Type: {market.get('type', 'unknown')}")

            print(f"📊 Tổng cộng tìm thấy {len(usdt_pairs)} cặp USDT")

            # Filter out leveraged tokens, margin tokens, etc.
            filtered_pairs = [pair for pair in usdt_pairs if not any(x in pair for x in ['UP/', 'DOWN/', 'BULL/', 'BEAR/', '3L/', '3S/', '2L/', '2S/'])]
            print(f"🧹 Sau khi lọc: {len(filtered_pairs)} cặp")

            # Use a predefined list of popular coins to ensure we have data
            popular_coins = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'XRP/USDT', 'ADA/USDT',
                           'SOL/USDT', 'DOGE/USDT', 'MATIC/USDT', 'DOT/USDT', 'AVAX/USDT',
                           'SHIB/USDT', 'LTC/USDT', 'ATOM/USDT', 'LINK/USDT', 'UNI/USDT',
                           'TRX/USDT', 'ETC/USDT', 'XLM/USDT', 'ALGO/USDT', 'VET/USDT']

            # Filter to only include popular coins that exist in the market
            final_pairs = [pair for pair in popular_coins if pair in filtered_pairs]
            print(f"🎯 Coin phổ biến có sẵn: {len(final_pairs)} - {final_pairs[:5]}...")

            # If we don't have enough popular coins, add others
            if len(final_pairs) < self.max_coins_to_scan:
                remaining_pairs = [pair for pair in filtered_pairs if pair not in final_pairs]
                final_pairs.extend(remaining_pairs[:self.max_coins_to_scan - len(final_pairs)])

            result = final_pairs[:self.max_coins_to_scan]
            print(f"✅ Sẽ phân tích {len(result)} coin: {result[:10]}...")
            return result

        except Exception as e:
            print(f"❌ Error getting USDT pairs: {e}")
            import traceback
            traceback.print_exc()
            return []

    def fetch_ohlcv(self, symbol, limit=100):
        """Fetch OHLCV data from exchange for a given symbol"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, self.timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            return df
        except Exception as e:
            print(f"Error fetching data for {symbol}: {e}")
            return None

    def calculate_indicators(self, df):
        """Calculate technical indicators using ta library"""
        # RSI
        df['RSI'] = ta.momentum.RSIIndicator(df['close'], window=14).rsi()

        # MACD
        macd_indicator = ta.trend.MACD(df['close'])
        df['MACD'] = macd_indicator.macd()
        df['MACD_signal'] = macd_indicator.macd_signal()
        df['MACD_hist'] = macd_indicator.macd_diff()

        # Bollinger Bands
        bb_indicator = ta.volatility.BollingerBands(df['close'], window=20)
        df['BB_upper'] = bb_indicator.bollinger_hband()
        df['BB_middle'] = bb_indicator.bollinger_mavg()
        df['BB_lower'] = bb_indicator.bollinger_lband()

        # Stochastic Oscillator
        stoch_indicator = ta.momentum.StochasticOscillator(df['high'], df['low'], df['close'], window=14, smooth_window=3)
        df['K'] = stoch_indicator.stoch()
        df['D'] = stoch_indicator.stoch_signal()

        # ADX - Trend Strength
        df['ADX'] = ta.trend.ADXIndicator(df['high'], df['low'], df['close'], window=14).adx()

        # ATR
        df['ATR'] = ta.volatility.AverageTrueRange(df['high'], df['low'], df['close'], window=14).average_true_range()

        # Fibonacci Retracement Levels (based on recent 20 periods)
        max_price = df['high'].rolling(window=20).max()
        min_price = df['low'].rolling(window=20).min()
        diff = max_price - min_price
        df['fib_0'] = min_price
        df['fib_23.6'] = min_price + 0.236 * diff
        df['fib_38.2'] = min_price + 0.382 * diff
        df['fib_50'] = min_price + 0.5 * diff
        df['fib_61.8'] = min_price + 0.618 * diff
        df['fib_100'] = max_price

        return df

    def calculate_position_size(self, coin_data):
        """Calculate optimal position size based on target daily profit"""
        try:
            # Extract data
            win_rate = coin_data['win_rate'] / 100  # Convert to decimal
            profit_pct = coin_data.get('profit_potential_pct', 0) / 100  # Convert to decimal
            risk_pct = coin_data.get('risk_pct', 0) / 100  # Convert to decimal
            entry_price = coin_data['entry_price']

            # Calculate expected profit per trade (accounting for win rate)
            expected_profit_pct = (profit_pct * win_rate) - (risk_pct * (1 - win_rate))

            # Account for trading fees (round trip = 2 * fee)
            fee_cost_pct = (self.trading_fee_pct * 2) / 100
            net_expected_profit_pct = expected_profit_pct - fee_cost_pct

            if net_expected_profit_pct <= 0:
                return None

            # Calculate target profit per trade
            target_profit_per_trade = self.daily_target_usd / self.trades_per_day

            # Calculate required position size (without leverage)
            required_position_size = target_profit_per_trade / net_expected_profit_pct

            # Calculate with different leverage options
            leverage_options = []
            for leverage in [1, 2, 3, 5, 10]:
                if leverage <= self.max_leverage:
                    margin_required = required_position_size / leverage
                    leverage_options.append({
                        'leverage': leverage,
                        'position_size': required_position_size,
                        'margin_required': margin_required,
                        'quantity': required_position_size / entry_price if entry_price > 0 else 0
                    })

            return {
                'target_profit_per_trade': target_profit_per_trade,
                'expected_profit_pct': expected_profit_pct * 100,
                'net_expected_profit_pct': net_expected_profit_pct * 100,
                'fee_cost_pct': fee_cost_pct * 100,
                'leverage_options': leverage_options,
                'recommended_leverage': min(3, self.max_leverage)  # Conservative recommendation
            }

        except Exception as e:
            print(f"Error calculating position size: {e}")
            return None

    def generate_signals(self, df):
        """Generate trading signals based on technical indicators"""
        signals = []
        signal_details = {}

        current_price = df['close'].iloc[-1]

        # RSI signals - Điều chỉnh để cân bằng LONG/SHORT
        if df['RSI'].iloc[-1] < 35:  # Tăng từ 30 lên 35 để có nhiều LONG hơn
            signal = "RSI oversold - Potential LONG"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'LONG',
                'indicator': 'RSI',
                'strength': min(100, 100 - df['RSI'].iloc[-1] * 2),  # Strength based on how oversold
                'entry_price': current_price,
                'target_price': current_price * 1.02,  # 2% profit target
                'stop_loss': current_price * 0.99  # 1% stop loss
            }
        elif df['RSI'].iloc[-1] > 65:  # Giảm từ 70 xuống 65 để có nhiều SHORT hơn
            signal = "RSI overbought - Potential SHORT"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'SHORT',
                'indicator': 'RSI',
                'strength': min(100, (df['RSI'].iloc[-1] - 65) * 3),  # Strength based on how overbought
                'entry_price': current_price,
                'target_price': current_price * 0.98,  # 2% profit target
                'stop_loss': current_price * 1.01  # 1% stop loss
            }

        # MACD signals
        if df['MACD'].iloc[-1] > df['MACD_signal'].iloc[-1] and \
           df['MACD'].iloc[-2] <= df['MACD_signal'].iloc[-2]:
            signal = "MACD bullish crossover - Potential LONG"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'LONG',
                'indicator': 'MACD',
                'strength': min(100, abs(df['MACD'].iloc[-1] - df['MACD_signal'].iloc[-1]) * 200),
                'entry_price': current_price,
                'target_price': current_price * 1.03,  # 3% profit target
                'stop_loss': current_price * 0.985  # 1.5% stop loss
            }
        elif df['MACD'].iloc[-1] < df['MACD_signal'].iloc[-1] and \
             df['MACD'].iloc[-2] >= df['MACD_signal'].iloc[-2]:
            signal = "MACD bearish crossover - Potential SHORT"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'SHORT',
                'indicator': 'MACD',
                'strength': min(100, abs(df['MACD'].iloc[-1] - df['MACD_signal'].iloc[-1]) * 200),
                'entry_price': current_price,
                'target_price': current_price * 0.97,  # 3% profit target
                'stop_loss': current_price * 1.015  # 1.5% stop loss
            }

        # Bollinger Bands signals
        if df['close'].iloc[-1] < df['BB_lower'].iloc[-1]:
            signal = "Price below lower BB - Potential LONG"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'LONG',
                'indicator': 'BB',
                'strength': min(100, (df['BB_lower'].iloc[-1] - df['close'].iloc[-1]) / df['close'].iloc[-1] * 1000),
                'entry_price': current_price,
                'target_price': df['BB_middle'].iloc[-1],  # Target middle band
                'stop_loss': current_price * 0.98  # 2% stop loss
            }
        elif df['close'].iloc[-1] > df['BB_upper'].iloc[-1]:
            signal = "Price above upper BB - Potential SHORT"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'SHORT',
                'indicator': 'BB',
                'strength': min(100, (df['close'].iloc[-1] - df['BB_upper'].iloc[-1]) / df['close'].iloc[-1] * 1000),
                'entry_price': current_price,
                'target_price': df['BB_middle'].iloc[-1],  # Target middle band
                'stop_loss': current_price * 1.02  # 2% stop loss
            }

        # Stochastic Oscillator signals - Điều chỉnh để cân bằng LONG/SHORT
        if df['K'].iloc[-1] < 25 and df['K'].iloc[-1] > df['K'].iloc[-2] and df['K'].iloc[-1] > df['D'].iloc[-1]:
            signal = "Stochastic oversold with bullish crossover - Strong LONG"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'LONG',
                'indicator': 'Stochastic',
                'strength': 85,  # High strength signal
                'entry_price': current_price,
                'target_price': current_price * 1.04,  # 4% profit target
                'stop_loss': current_price * 0.98  # 2% stop loss
            }
        elif df['K'].iloc[-1] > 75 and df['K'].iloc[-1] < df['K'].iloc[-2] and df['K'].iloc[-1] < df['D'].iloc[-1]:
            signal = "Stochastic overbought with bearish crossover - Strong SHORT"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'SHORT',
                'indicator': 'Stochastic',
                'strength': 85,  # High strength signal
                'entry_price': current_price,
                'target_price': current_price * 0.96,  # 4% profit target
                'stop_loss': current_price * 1.02  # 2% stop loss
            }

        # Combined signals from File 1 (RSI, ADX, MACD crossover)
        rsi = df['RSI'].iloc[-1]
        adx = df['ADX'].iloc[-1]
        macd = df['MACD'].iloc[-1]
        macd_signal = df['MACD_signal'].iloc[-1]
        macd_prev = df['MACD'].iloc[-2]
        macd_signal_prev = df['MACD_signal'].iloc[-2]
        atr = df['ATR'].iloc[-1]

        if rsi < 35 and adx > 20 and macd > macd_signal and macd_prev <= macd_signal_prev:
            signal = "Combined LONG (RSI, ADX, MACD)"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'LONG',
                'indicator': 'Combined',
                'strength': 90, # High strength
                'entry_price': current_price,
                'target_price': current_price + 2.5 * atr,
                'stop_loss': current_price - 1.5 * atr
            }
        elif rsi > 65 and adx > 20 and macd < macd_signal and macd_prev >= macd_signal_prev:
            signal = "Combined SHORT (RSI, ADX, MACD)"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'SHORT',
                'indicator': 'Combined',
                'strength': 90, # High strength
                'entry_price': current_price,
                'target_price': current_price - 2.5 * atr,
                'stop_loss': current_price + 1.5 * atr
            }

        # Thêm tín hiệu SHORT dựa trên xu hướng giảm
        # Nếu giá đang giảm và RSI > 50 (không quá oversold)
        price_change_5 = (df['close'].iloc[-1] / df['close'].iloc[-6] - 1) * 100  # % change over 5 periods
        if price_change_5 < -2 and rsi > 50 and rsi < 70:  # Giá giảm > 2% và RSI trung tính
            signal = "Downtrend continuation - Potential SHORT"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'SHORT',
                'indicator': 'Trend',
                'strength': min(100, abs(price_change_5) * 10),
                'entry_price': current_price,
                'target_price': current_price * 0.975,  # 2.5% profit target
                'stop_loss': current_price * 1.015  # 1.5% stop loss
            }

        # Tín hiệu LONG dựa trên xu hướng tăng
        elif price_change_5 > 2 and rsi < 50 and rsi > 30:  # Giá tăng > 2% và RSI trung tính
            signal = "Uptrend continuation - Potential LONG"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'LONG',
                'indicator': 'Trend',
                'strength': min(100, price_change_5 * 10),
                'entry_price': current_price,
                'target_price': current_price * 1.025,  # 2.5% profit target
                'stop_loss': current_price * 0.985  # 1.5% stop loss
            }

        return signals, signal_details

    def calculate_optimal_entry(self, df, signal_type, current_price):
        """Calculate optimal entry price based on recent price action and Fibonacci levels"""
        if signal_type == 'LONG':
            # For long positions, look for support levels
            recent_lows = df['low'].iloc[-20:].nsmallest(3).values
            fib_supports = [df['fib_0'].iloc[-1], df['fib_23.6'].iloc[-1], df['fib_38.2'].iloc[-1]]

            # Combine all potential support levels
            support_levels = list(recent_lows) + fib_supports
            support_levels = [s for s in support_levels if s < current_price]  # Only levels below current price

            if not support_levels:
                return current_price  # No better entry found

            # Find closest support level to current price
            optimal_entry = max(support_levels)

            # Don't suggest entry too far from current price
            if optimal_entry < current_price * 0.97:
                optimal_entry = current_price * 0.97

            return optimal_entry
        else:  # SHORT
            # For short positions, look for resistance levels
            recent_highs = df['high'].iloc[-20:].nlargest(3).values
            fib_resistances = [df['fib_61.8'].iloc[-1], df['fib_100'].iloc[-1]]

            # Combine all potential resistance levels
            resistance_levels = list(recent_highs) + fib_resistances
            resistance_levels = [r for r in resistance_levels if r > current_price]  # Only levels above current price

            if not resistance_levels:
                return current_price  # No better entry found

            # Find closest resistance level to current price
            optimal_entry = min(resistance_levels)

            # Don't suggest entry too far from current price
            if optimal_entry > current_price * 1.03:
                optimal_entry = current_price * 1.03

            return optimal_entry

    def get_high_probability_signals(self, df, signals, signal_details):
        """Identify high probability signals based on historical win rate"""
        high_prob_signals = []

        try:
            # For testing purposes, simulate win rates instead of backtesting
            # This will be replaced with actual backtesting in production
            for signal in signals:
                signal_info = signal_details[signal]
                indicator = signal_info['indicator']
                signal_type = signal_info['type']

                # Generate a unique key for this signal type
                signal_key = f"{indicator}_{signal_type}"

                # Simulate win rates for testing
                if 'RSI' in signal and 'SHORT' in signal:
                    win_rate = 75.5
                    sample_size = 12
                elif 'RSI' in signal and 'LONG' in signal:
                    win_rate = 68.2
                    sample_size = 8
                elif 'MACD' in signal and 'SHORT' in signal:
                    win_rate = 72.0
                    sample_size = 15
                elif 'MACD' in signal and 'LONG' in signal:
                    win_rate = 65.5
                    sample_size = 10
                elif 'BB' in signal and 'SHORT' in signal:
                    win_rate = 70.0
                    sample_size = 14
                elif 'BB' in signal and 'LONG' in signal:
                    win_rate = 67.5
                    sample_size = 9
                elif 'Stochastic' in signal and 'SHORT' in signal:
                    win_rate = 78.0
                    sample_size = 11
                elif 'Stochastic' in signal and 'LONG' in signal:
                    win_rate = 73.5
                    sample_size = 7
                elif 'Combined' in signal and 'SHORT' in signal:
                    win_rate = 80.0
                    sample_size = 15
                elif 'Combined' in signal and 'LONG' in signal:
                    win_rate = 78.0
                    sample_size = 14
                else:
                    win_rate = 62.0
                    sample_size = 6

                # Store in signal history
                self.signal_history[signal_key] = {
                    'win_rate': win_rate,
                    'sample_size': sample_size,
                    'last_updated': datetime.now()
                }

                # Only include signals with sufficient win rate and sample size
                if win_rate >= self.win_rate_threshold and sample_size >= 5:
                    # Calculate optimal entry price
                    optimal_entry = self.calculate_optimal_entry(df, signal_type, signal_info['entry_price'])

                    # Update signal details with win rate and optimal entry
                    signal_info['win_rate'] = win_rate
                    signal_info['sample_size'] = sample_size
                    signal_info['optimal_entry'] = optimal_entry

                    # Calculate dynamic SL/TP using ATR
                    atr = df['ATR'].iloc[-1]
                    current_price = optimal_entry

                    if signal_type == 'LONG':
                        # Dynamic Stop Loss: 1.5 * ATR below entry
                        signal_info['stop_loss'] = current_price - (1.5 * atr)
                        # Dynamic Take Profit: 2.5 * ATR above entry (1:1.67 risk-reward)
                        signal_info['target_price'] = current_price + (2.5 * atr)

                        risk = current_price - signal_info['stop_loss']
                        reward = signal_info['target_price'] - current_price
                    else:  # SHORT
                        # Dynamic Stop Loss: 1.5 * ATR above entry
                        signal_info['stop_loss'] = current_price + (1.5 * atr)
                        # Dynamic Take Profit: 2.5 * ATR below entry
                        signal_info['target_price'] = current_price - (2.5 * atr)

                        risk = signal_info['stop_loss'] - current_price
                        reward = current_price - signal_info['target_price']

                    signal_info['risk_reward_ratio'] = reward / risk if risk > 0 else 0
                    signal_info['atr_value'] = atr

                    # Add to high probability signals
                    high_prob_signals.append((signal, signal_info))

            # Sort by win rate and risk-reward ratio
            high_prob_signals.sort(key=lambda x: (x[1]['win_rate'] * x[1]['risk_reward_ratio']), reverse=True)

        except Exception as e:
            print(f"Error in get_high_probability_signals: {e}")

        return high_prob_signals

    def analyze_coin(self, symbol):
        """Analyze a single coin and return its potential"""
        try:
            # Fetch data
            df = self.fetch_ohlcv(symbol, limit=100)
            if df is None or len(df) < 50:  # Need enough data for analysis
                return None

            # Calculate indicators
            df = self.calculate_indicators(df)

            # Generate signals
            signals, signal_details = self.generate_signals(df)

            # Get high probability signals
            high_prob_signals = self.get_high_probability_signals(df, signals, signal_details)

            if not high_prob_signals:
                return None

            # Get the best signal
            best_signal, best_info = high_prob_signals[0]

            # Calculate a score based on win rate, risk-reward ratio, and sample size
            score = best_info['win_rate'] * best_info['risk_reward_ratio'] * min(1, best_info['sample_size'] / 10)

            # Calculate volatility (as a positive factor for potential profit)
            volatility = df['high'].pct_change().abs().mean() * 100  # Average absolute % change

            # Calculate volume (higher volume means more liquidity)
            avg_volume = df['volume'].mean()

            # Calculate momentum (recent price direction)
            momentum = (df['close'].iloc[-1] / df['close'].iloc[-20] - 1) * 100  # % change over last 20 periods

            # Calculate profit/loss percentages
            if best_info['type'] == 'LONG':
                profit_potential_pct = ((best_info['target_price'] - best_info['optimal_entry']) / best_info['optimal_entry']) * 100
                risk_pct = ((best_info['optimal_entry'] - best_info['stop_loss']) / best_info['optimal_entry']) * 100
            else:  # SHORT
                profit_potential_pct = ((best_info['optimal_entry'] - best_info['target_price']) / best_info['optimal_entry']) * 100
                risk_pct = ((best_info['stop_loss'] - best_info['optimal_entry']) / best_info['optimal_entry']) * 100

            # Calculate a combined potential score
            potential_score = score * (1 + volatility/100) * (1 + momentum/100 if best_info['type'] == 'LONG' else 1 - momentum/100)

            coin_data = {
                'symbol': symbol,
                'score': potential_score,
                'win_rate': best_info['win_rate'],
                'risk_reward': best_info['risk_reward_ratio'],
                'signal_type': best_info['type'],
                'indicator': best_info['indicator'],
                'entry_price': best_info['optimal_entry'],
                'target_price': best_info['target_price'],
                'stop_loss': best_info['stop_loss'],
                'volatility': volatility,
                'momentum': momentum,
                'volume': avg_volume,
                'profit_potential_pct': profit_potential_pct,
                'risk_pct': risk_pct
            }

            # Calculate position size
            position_info = self.calculate_position_size(coin_data)
            if position_info:
                coin_data['position_info'] = position_info

            return coin_data
        except Exception as e:
            print(f"Error analyzing {symbol}: {e}")
            return None

    def scan_for_opportunities(self):
        """Scan all available coins for trading opportunities"""
        print("\n🔍 Đang quét thị trường để tìm các coin tiềm năng...")

        # Get all USDT trading pairs (futures for Bybit)
        pairs = self.get_usdt_pairs(exchange_type='future')
        print(f"Tìm thấy {len(pairs)} cặp giao dịch USDT")

        # Analyze each coin in parallel
        results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            future_to_symbol = {executor.submit(self.analyze_coin, symbol): symbol for symbol in pairs}
            for future in concurrent.futures.as_completed(future_to_symbol):
                result = future.result()
                if result:
                    results.append(result)

        # Sort by potential score
        results.sort(key=lambda x: x['score'], reverse=True)

        # Update top coins
        self.top_coins = results[:self.top_n]

        # Update last scan time
        self.last_scan_time = time.time()

        return self.top_coins

    def display_opportunities(self, opportunities):
        """Display the trading opportunities"""
        if not opportunities:
            print("Không có coin nào đạt đủ điều kiện trong thời điểm này!")
            return

        print("\n🚀 CÁC CƠ HỘI GIAO DỊCH TIỀM NĂNG:")

        # Prepare table data
        table_data = []
        for coin in opportunities:
            current_price = coin['entry_price']
            profit_potential = coin.get('profit_potential_pct', 0)
            risk = coin.get('risk_pct', 0)

            # Get position info if available
            position_size = "N/A"
            margin_required = "N/A"
            if 'position_info' in coin and coin['position_info']:
                pos_info = coin['position_info']
                recommended_lev = pos_info['recommended_leverage']
                for lev_option in pos_info['leverage_options']:
                    if lev_option['leverage'] == recommended_lev:
                        position_size = f"${lev_option['position_size']:.0f}"
                        margin_required = f"${lev_option['margin_required']:.0f}"
                        break

            table_data.append([
                coin['symbol'],
                f"{coin['signal_type']} ({coin['indicator']})",
                f"{coin['win_rate']:.1f}%",
                f"{coin['risk_reward']:.2f}",
                f"{current_price:.4f}",
                f"{coin['target_price']:.4f}",
                f"{coin['stop_loss']:.4f}",
                f"{profit_potential:.2f}%",
                f"{risk:.2f}%",
                position_size,
                margin_required
            ])

        # Print table
        headers = ["Coin", "Tín hiệu", "Tỉ lệ thắng", "R/R", "Giá vào", "TP (ATR)", "SL (ATR)", "Lợi nhuận", "Rủi ro", "Position", "Margin"]
        print(tabulate(table_data, headers=headers, tablefmt="grid"))

        # Print detailed information for the top coin if in auto_scan mode
        if self.auto_scan and self.top_coins:
            top_coin = self.top_coins[0]
            print(f"\n💎 CƠ HỘI TỐT NHẤT: {top_coin['symbol']}")
            print(f"   📊 Loại lệnh: {top_coin['signal_type']}")
            print(f"   🎯 Chỉ báo: {top_coin['indicator']}")
            print(f"   ✅ Tỉ lệ thắng: {top_coin['win_rate']:.1f}%")
            print(f"   💰 Tỉ lệ lợi nhuận/rủi ro: {top_coin['risk_reward']:.2f}")
            print(f"   🔵 Giá vào lệnh: {top_coin['entry_price']:.6f} USDT")
            print(f"   🟢 Take Profit (ATR): {top_coin['target_price']:.6f} USDT")
            print(f"   🔴 Stop Loss (ATR): {top_coin['stop_loss']:.6f} USDT")
            print(f"   📈 Biến động: {top_coin['volatility']:.2f}%")
            print(f"   🔄 Xu hướng: {top_coin['momentum']:.2f}%")

            # Calculate profit/loss percentages
            entry_price = top_coin['entry_price']
            if top_coin['signal_type'] == 'LONG':
                profit_pct = ((top_coin['target_price'] - entry_price) / entry_price) * 100
                loss_pct = ((entry_price - top_coin['stop_loss']) / entry_price) * 100
            else:
                profit_pct = ((entry_price - top_coin['target_price']) / entry_price) * 100
                loss_pct = ((top_coin['stop_loss'] - entry_price) / entry_price) * 100

            print(f"   💵 Lợi nhuận tiềm năng: +{profit_pct:.2f}%")
            print(f"   ⚠️  Rủi ro tối đa: -{loss_pct:.2f}%")

            # Display position sizing information
            if 'position_info' in top_coin and top_coin['position_info']:
                pos_info = top_coin['position_info']
                print(f"\n💰 TÍNH TOÁN POSITION SIZE (Target: ${self.daily_target_usd}/ngày):")
                print(f"   🎯 Target profit/trade: ${pos_info['target_profit_per_trade']:.2f}")
                print(f"   📊 Expected profit: {pos_info['expected_profit_pct']:.2f}%")
                print(f"   📉 Net profit (after fees): {pos_info['net_expected_profit_pct']:.2f}%")
                print(f"   💸 Trading fees: {pos_info['fee_cost_pct']:.2f}%")

                print(f"\n🔥 CÁC LỰA CHỌN ĐÒN BẨY:")
                for lev_option in pos_info['leverage_options']:
                    leverage = lev_option['leverage']
                    position_size = lev_option['position_size']
                    margin_required = lev_option['margin_required']
                    quantity = lev_option['quantity']

                    recommended = "⭐" if leverage == pos_info['recommended_leverage'] else "  "
                    print(f"   {recommended} {leverage}x: Position ${position_size:.0f} | Margin ${margin_required:.0f} | Qty {quantity:.4f}")

                print(f"\n📝 Lưu ý: Theo dõi funding rate khi hold qua đêm")
            else:
                print(f"   🔥 Đòn bẩy khuyến nghị: 3x-5x (Futures)")
                print(f"   📝 Lưu ý: Theo dõi funding rate khi hold qua đêm")

    def run(self):
        """Main bot loop"""
        print(f"Starting trading bot on {self.exchange.id} for {self.timeframe} timeframe")
        if self.auto_scan:
            print(f"Auto scan for opportunities: Enabled (scanning every {self.scan_interval // 60} minutes)")
        else:
            print(f"Analyzing specific symbol: {self.specific_symbol}")

        while True:
            try:
                if self.auto_scan:
                    print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Bắt đầu scan thị trường...")
                    opportunities = self.scan_for_opportunities()
                    self.display_opportunities(opportunities)

                    print(f"\n💤 Chờ {self.scan_interval // 60} phút cho lần scan tiếp theo...")
                    time.sleep(self.scan_interval)
                else:
                    # Analyze specific symbol
                    if self.specific_symbol:
                        print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Phân tích {self.specific_symbol}...")
                        result = self.analyze_coin(self.specific_symbol)
                        if result:
                            self.display_opportunities([result])
                        else:
                            print(f"Không có tín hiệu cho {self.specific_symbol} tại thời điểm này.")
                    time.sleep(self.scan_interval)

            except KeyboardInterrupt:
                print("\n👋 Bot đã dừng!")
                break
            except Exception as e:
                print(f"❌ Lỗi trong main loop: {e}")
                import traceback
                traceback.print_exc()
                time.sleep(60)  # Wait before retrying

if __name__ == '__main__':
    # Scan top 10 coins with highest win rate on Bybit every hour
    print("🚀 Khởi động bot phân tích 10 coin có tỉ lệ thắng cao nhất!")
    print("📊 Sàn giao dịch: Bybit Futures (USDT Perpetual)")
    print("⏰ Khung thời gian: 1 giờ")
    print("🔄 Cập nhật mỗi: 5 phút (test mode)")
    print("🎯 Số coin phân tích: Top 10")
    print("💡 SL/TP động dựa trên ATR (Average True Range)")
    print("🎲 Tỉ lệ Risk/Reward: 1:1.67 (SL: 1.5*ATR, TP: 2.5*ATR)")
    print("⚡ Hỗ trợ Long/Short với đòn bẩy")
    print("💰 Target lợi nhuận: $20/ngày")
    print("🧮 Tự động tính Position Size & Margin cần thiết")
    print("-" * 60)

    bot = CryptoTradingBot(
        exchange_id='bybit',
        timeframe='1h',
        scan_interval=300,  # 5 phút để test nhanh
        top_n=10,
        auto_scan=True,
        daily_target_usd=20,  # Target $20/day
        max_leverage=5,       # Max 5x leverage
        trading_fee_pct=0.1   # 0.1% trading fee
    )
    bot.run()
