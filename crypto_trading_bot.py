import ccxt
import pandas as pd
import numpy as np
import talib
import time
from datetime import datetime

class CryptoTradingBot:
    def __init__(self, exchange_id='binance', timeframe='1h', scan_interval=3600, top_n=10):
        self.exchange = getattr(ccxt, exchange_id)({'enableRateLimit': True})
        self.timeframe = timeframe
        self.scan_interval = scan_interval
        self.top_n = top_n

    def get_usdt_pairs(self):
        markets = self.exchange.load_markets()
        return [
            s for s in markets
            if s.endswith('/USDT') and not any(x in s for x in ['UP/', 'DOWN/', 'BULL/', 'BEAR/', '3L/', '3S/'])
        ]

    def fetch_ohlcv(self, symbol, limit=100):
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, self.timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            return df
        except Exception:
            return None

    def calculate_indicators(self, df):
        df['RSI'] = talib.RSI(df['close'], timeperiod=14)
        macd, macd_signal, _ = talib.MACD(df['close'])
        df['MACD'] = macd
        df['MACD_signal'] = macd_signal
        df['ADX'] = talib.ADX(df['high'], df['low'], df['close'], timeperiod=14)
        df['ATR'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14)
        return df

    def analyze_coin(self, symbol):
        df = self.fetch_ohlcv(symbol)
        if df is None or len(df) < 30:
            return None

        df = self.calculate_indicators(df)
        close = df['close'].iloc[-1]
        atr = df['ATR'].iloc[-1]
        rsi = df['RSI'].iloc[-1]
        adx = df['ADX'].iloc[-1]
        macd = df['MACD'].iloc[-1]
        macd_signal = df['MACD_signal'].iloc[-1]
        macd_prev = df['MACD'].iloc[-2]
        macd_signal_prev = df['MACD_signal'].iloc[-2]

        signal = None
        score = 0
        sl = tp = None

        # Điều kiện LONG
        if rsi < 32 and adx > 20 and macd > macd_signal and macd_prev <= macd_signal_prev:
            signal = "LONG"
            score += 2
            sl = close - 1.5 * atr
            tp = close + 2.5 * atr

        # Điều kiện SHORT
        elif rsi > 68 and adx > 20 and macd < macd_signal and macd_prev >= macd_signal_prev:
            signal = "SHORT"
            score += 2
            sl = close + 1.5 * atr
            tp = close - 2.5 * atr

        # Bonus: tăng score nếu ATR lớn (biến động mạnh)
        score += float(atr) / close

        if not signal:
            return None  # Chỉ trả về coin có tín hiệu vào lệnh

        return {
            'symbol': symbol,
            'signal': signal,
            'score': score,
            'entry': close,
            'sl': sl,
            'tp': tp,
            'rsi': rsi,
            'adx': adx,
            'atr': atr
        }

    def scan_top_coins(self):
        usdt_pairs = self.get_usdt_pairs()
        results = []

        print(f"\n=== Scan thị trường: {len(usdt_pairs)} coin USDT ===")
        for symbol in usdt_pairs:
            result = self.analyze_coin(symbol)
            if result:
                results.append(result)

        # Sắp xếp theo điểm số giảm dần, lấy top n
        top_coins = sorted(results, key=lambda x: x['score'], reverse=True)[:self.top_n]
        return top_coins

    def run(self):
        while True:
            top_coins = self.scan_top_coins()
            print(f"\n=== Top {self.top_n} coin có tín hiệu vào lệnh ===")
            print("Coin        | Signal | Entry    | SL       | TP       | RSI   | ADX")
            print("-" * 68)
            for coin in top_coins:
                print(f"{coin['symbol']:12} | {coin['signal']:6} | {coin['entry']:.4f} | {coin['sl']:.4f} | {coin['tp']:.4f} | {coin['rsi']:.2f} | {coin['adx']:.2f}")
            if not top_coins:
                print("Không có coin nào đạt đủ điều kiện trong thời điểm này!")
            print(f"\nCập nhật tiếp theo sau {self.scan_interval // 60} phút...")
            time.sleep(self.scan_interval)

if __name__ == '__main__':
    bot = CryptoTradingBot(scan_interval=3600, top_n=10)
    bot.run()
