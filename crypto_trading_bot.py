import ccxt
import pandas as pd
import numpy as np
import ta
from datetime import datetime
import time
import statistics
import concurrent.futures
from tabulate import tabulate

class CryptoTradingBot:
    def __init__(self, exchange_id='binance', timeframe='1h', scan_interval=3600, top_n=10, auto_scan=True, specific_symbol=None):
        self.exchange = getattr(ccxt, exchange_id)({
            'enableRateLimit': True,
        })
        self.timeframe = timeframe
        self.scan_interval = scan_interval
        self.top_n = top_n  # Used for top coins in auto_scan mode
        self.auto_scan = auto_scan
        self.specific_symbol = specific_symbol  # Used when auto_scan is False

        self.signal_history = {}
        self.win_rate_threshold = 60  # Minimum win rate for high probability signals (%)
        self.top_coins = []
        self.last_scan_time = 0
        self.max_coins_to_scan = 100  # Max coins to scan in auto_scan mode

    def get_usdt_pairs(self, exchange_type='future'):
        """Get all available USDT trading pairs from the exchange, with option for futures/swap"""
        try:
            markets = self.exchange.load_markets()
            usdt_pairs = []
            for symbol in markets.keys():
                market = markets[symbol]
                if symbol.endswith('/USDT') and '/USDT' in symbol:
                    if exchange_type == 'future':
                        if (market.get('type') in ['future', 'swap'] or market.get('future', False)):
                            usdt_pairs.append(symbol)
                    else: # spot or all
                        usdt_pairs.append(symbol)

            # Filter out leveraged tokens, margin tokens, etc.
            filtered_pairs = [pair for pair in usdt_pairs if not any(x in pair for x in ['UP/', 'DOWN/', 'BULL/', 'BEAR/', '3L/', '3S/'])]

            # Sort by volume if available
            if hasattr(self.exchange, 'fetch_tickers'):
                try:
                    tickers = self.exchange.fetch_tickers(filtered_pairs)
                    pairs_with_volume = [(symbol, tickers[symbol]['quoteVolume'] if 'quoteVolume' in tickers[symbol] else 0)
                                        for symbol in filtered_pairs if symbol in tickers]
                    pairs_with_volume.sort(key=lambda x: x[1], reverse=True)
                    return [pair[0] for pair in pairs_with_volume[:self.max_coins_to_scan]]
                except Exception as e:
                    print(f"Error fetching tickers for volume sort: {e}")
                    return filtered_pairs[:self.max_coins_to_scan]
            return filtered_pairs[:self.max_coins_to_scan]
        except Exception as e:
            print(f"Error getting USDT pairs: {e}")
            return []

    def fetch_ohlcv(self, symbol, limit=100):
        """Fetch OHLCV data from exchange for a given symbol"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, self.timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            return df
        except Exception as e:
            print(f"Error fetching data for {symbol}: {e}")
            return None

    def calculate_indicators(self, df):
        """Calculate technical indicators using ta library"""
        # RSI
        df['RSI'] = ta.momentum.RSIIndicator(df['close'], window=14).rsi()

        # MACD
        macd_indicator = ta.trend.MACD(df['close'])
        df['MACD'] = macd_indicator.macd()
        df['MACD_signal'] = macd_indicator.macd_signal()
        df['MACD_hist'] = macd_indicator.macd_diff()

        # Bollinger Bands
        bb_indicator = ta.volatility.BollingerBands(df['close'], window=20)
        df['BB_upper'] = bb_indicator.bollinger_hband()
        df['BB_middle'] = bb_indicator.bollinger_mavg()
        df['BB_lower'] = bb_indicator.bollinger_lband()

        # Stochastic Oscillator
        stoch_indicator = ta.momentum.StochasticOscillator(df['high'], df['low'], df['close'], window=14, smooth_window=3)
        df['K'] = stoch_indicator.stoch()
        df['D'] = stoch_indicator.stoch_signal()

        # ADX - Trend Strength
        df['ADX'] = ta.trend.ADXIndicator(df['high'], df['low'], df['close'], window=14).adx()

        # ATR
        df['ATR'] = ta.volatility.AverageTrueRange(df['high'], df['low'], df['close'], window=14).average_true_range()

        # Fibonacci Retracement Levels (based on recent 20 periods)
        max_price = df['high'].rolling(window=20).max()
        min_price = df['low'].rolling(window=20).min()
        diff = max_price - min_price
        df['fib_0'] = min_price
        df['fib_23.6'] = min_price + 0.236 * diff
        df['fib_38.2'] = min_price + 0.382 * diff
        df['fib_50'] = min_price + 0.5 * diff
        df['fib_61.8'] = min_price + 0.618 * diff
        df['fib_100'] = max_price

        return df

    def generate_signals(self, df):
        """Generate trading signals based on technical indicators"""
        signals = []
        signal_details = {}

        current_price = df['close'].iloc[-1]

        # RSI signals
        if df['RSI'].iloc[-1] < 30:
            signal = "RSI oversold - Potential LONG"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'LONG',
                'indicator': 'RSI',
                'strength': min(100, 100 - df['RSI'].iloc[-1] * 2),  # Strength based on how oversold
                'entry_price': current_price,
                'target_price': current_price * 1.02,  # 2% profit target
                'stop_loss': current_price * 0.99  # 1% stop loss
            }
        elif df['RSI'].iloc[-1] > 70:
            signal = "RSI overbought - Potential SHORT"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'SHORT',
                'indicator': 'RSI',
                'strength': min(100, (df['RSI'].iloc[-1] - 70) * 3),  # Strength based on how overbought
                'entry_price': current_price,
                'target_price': current_price * 0.98,  # 2% profit target
                'stop_loss': current_price * 1.01  # 1% stop loss
            }

        # MACD signals
        if df['MACD'].iloc[-1] > df['MACD_signal'].iloc[-1] and \
           df['MACD'].iloc[-2] <= df['MACD_signal'].iloc[-2]:
            signal = "MACD bullish crossover - Potential LONG"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'LONG',
                'indicator': 'MACD',
                'strength': min(100, abs(df['MACD'].iloc[-1] - df['MACD_signal'].iloc[-1]) * 200),
                'entry_price': current_price,
                'target_price': current_price * 1.03,  # 3% profit target
                'stop_loss': current_price * 0.985  # 1.5% stop loss
            }
        elif df['MACD'].iloc[-1] < df['MACD_signal'].iloc[-1] and \
             df['MACD'].iloc[-2] >= df['MACD_signal'].iloc[-2]:
            signal = "MACD bearish crossover - Potential SHORT"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'SHORT',
                'indicator': 'MACD',
                'strength': min(100, abs(df['MACD'].iloc[-1] - df['MACD_signal'].iloc[-1]) * 200),
                'entry_price': current_price,
                'target_price': current_price * 0.97,  # 3% profit target
                'stop_loss': current_price * 1.015  # 1.5% stop loss
            }

        # Bollinger Bands signals
        if df['close'].iloc[-1] < df['BB_lower'].iloc[-1]:
            signal = "Price below lower BB - Potential LONG"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'LONG',
                'indicator': 'BB',
                'strength': min(100, (df['BB_lower'].iloc[-1] - df['close'].iloc[-1]) / df['close'].iloc[-1] * 1000),
                'entry_price': current_price,
                'target_price': df['BB_middle'].iloc[-1],  # Target middle band
                'stop_loss': current_price * 0.98  # 2% stop loss
            }
        elif df['close'].iloc[-1] > df['BB_upper'].iloc[-1]:
            signal = "Price above upper BB - Potential SHORT"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'SHORT',
                'indicator': 'BB',
                'strength': min(100, (df['close'].iloc[-1] - df['BB_upper'].iloc[-1]) / df['close'].iloc[-1] * 1000),
                'entry_price': current_price,
                'target_price': df['BB_middle'].iloc[-1],  # Target middle band
                'stop_loss': current_price * 1.02  # 2% stop loss
            }

        # Stochastic Oscillator signals
        if df['K'].iloc[-1] < 20 and df['K'].iloc[-1] > df['K'].iloc[-2] and df['K'].iloc[-1] > df['D'].iloc[-1]:
            signal = "Stochastic oversold with bullish crossover - Strong LONG"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'LONG',
                'indicator': 'Stochastic',
                'strength': 85,  # High strength signal
                'entry_price': current_price,
                'target_price': current_price * 1.04,  # 4% profit target
                'stop_loss': current_price * 0.98  # 2% stop loss
            }
        elif df['K'].iloc[-1] > 80 and df['K'].iloc[-1] < df['K'].iloc[-2] and df['K'].iloc[-1] < df['D'].iloc[-1]:
            signal = "Stochastic overbought with bearish crossover - Strong SHORT"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'SHORT',
                'indicator': 'Stochastic',
                'strength': 85,  # High strength signal
                'entry_price': current_price,
                'target_price': current_price * 0.96,  # 4% profit target
                'stop_loss': current_price * 1.02  # 2% stop loss
            }

        # Combined signals from File 1 (RSI, ADX, MACD crossover)
        rsi = df['RSI'].iloc[-1]
        adx = df['ADX'].iloc[-1]
        macd = df['MACD'].iloc[-1]
        macd_signal = df['MACD_signal'].iloc[-1]
        macd_prev = df['MACD'].iloc[-2]
        macd_signal_prev = df['MACD_signal'].iloc[-2]
        atr = df['ATR'].iloc[-1]

        if rsi < 32 and adx > 20 and macd > macd_signal and macd_prev <= macd_signal_prev:
            signal = "Combined LONG (RSI, ADX, MACD)"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'LONG',
                'indicator': 'Combined',
                'strength': 90, # High strength
                'entry_price': current_price,
                'target_price': current_price + 2.5 * atr,
                'stop_loss': current_price - 1.5 * atr
            }
        elif rsi > 68 and adx > 20 and macd < macd_signal and macd_prev >= macd_signal_prev:
            signal = "Combined SHORT (RSI, ADX, MACD)"
            signals.append(signal)
            signal_details[signal] = {
                'type': 'SHORT',
                'indicator': 'Combined',
                'strength': 90, # High strength
                'entry_price': current_price,
                'target_price': current_price - 2.5 * atr,
                'stop_loss': current_price + 1.5 * atr
            }

        return signals, signal_details

    def calculate_optimal_entry(self, df, signal_type, current_price):
        """Calculate optimal entry price based on recent price action and Fibonacci levels"""
        if signal_type == 'LONG':
            # For long positions, look for support levels
            recent_lows = df['low'].iloc[-20:].nsmallest(3).values
            fib_supports = [df['fib_0'].iloc[-1], df['fib_23.6'].iloc[-1], df['fib_38.2'].iloc[-1]]

            # Combine all potential support levels
            support_levels = list(recent_lows) + fib_supports
            support_levels = [s for s in support_levels if s < current_price]  # Only levels below current price

            if not support_levels:
                return current_price  # No better entry found

            # Find closest support level to current price
            optimal_entry = max(support_levels)

            # Don't suggest entry too far from current price
            if optimal_entry < current_price * 0.97:
                optimal_entry = current_price * 0.97

            return optimal_entry
        else:  # SHORT
            # For short positions, look for resistance levels
            recent_highs = df['high'].iloc[-20:].nlargest(3).values
            fib_resistances = [df['fib_61.8'].iloc[-1], df['fib_100'].iloc[-1]]

            # Combine all potential resistance levels
            resistance_levels = list(recent_highs) + fib_resistances
            resistance_levels = [r for r in resistance_levels if r > current_price]  # Only levels above current price

            if not resistance_levels:
                return current_price  # No better entry found

            # Find closest resistance level to current price
            optimal_entry = min(resistance_levels)

            # Don't suggest entry too far from current price
            if optimal_entry > current_price * 1.03:
                optimal_entry = current_price * 1.03

            return optimal_entry

    def get_high_probability_signals(self, df, signals, signal_details):
        """Identify high probability signals based on historical win rate"""
        high_prob_signals = []

        try:
            # For testing purposes, simulate win rates instead of backtesting
            # This will be replaced with actual backtesting in production
            for signal in signals:
                signal_info = signal_details[signal]
                indicator = signal_info['indicator']
                signal_type = signal_info['type']

                # Generate a unique key for this signal type
                signal_key = f"{indicator}_{signal_type}"

                # Simulate win rates for testing
                if 'RSI' in signal and 'SHORT' in signal:
                    win_rate = 75.5
                    sample_size = 12
                elif 'RSI' in signal and 'LONG' in signal:
                    win_rate = 68.2
                    sample_size = 8
                elif 'MACD' in signal and 'SHORT' in signal:
                    win_rate = 72.0
                    sample_size = 15
                elif 'MACD' in signal and 'LONG' in signal:
                    win_rate = 65.5
                    sample_size = 10
                elif 'BB' in signal and 'SHORT' in signal:
                    win_rate = 70.0
                    sample_size = 14
                elif 'BB' in signal and 'LONG' in signal:
                    win_rate = 67.5
                    sample_size = 9
                elif 'Stochastic' in signal and 'SHORT' in signal:
                    win_rate = 78.0
                    sample_size = 11
                elif 'Stochastic' in signal and 'LONG' in signal:
                    win_rate = 73.5
                    sample_size = 7
                elif 'Combined' in signal and 'SHORT' in signal:
                    win_rate = 80.0
                    sample_size = 15
                elif 'Combined' in signal and 'LONG' in signal:
                    win_rate = 78.0
                    sample_size = 14
                else:
                    win_rate = 62.0
                    sample_size = 6

                # Store in signal history
                self.signal_history[signal_key] = {
                    'win_rate': win_rate,
                    'sample_size': sample_size,
                    'last_updated': datetime.now()
                }

                # Only include signals with sufficient win rate and sample size
                if win_rate >= self.win_rate_threshold and sample_size >= 5:
                    # Calculate optimal entry price
                    optimal_entry = self.calculate_optimal_entry(df, signal_type, signal_info['entry_price'])

                    # Update signal details with win rate and optimal entry
                    signal_info['win_rate'] = win_rate
                    signal_info['sample_size'] = sample_size
                    signal_info['optimal_entry'] = optimal_entry

                    # Adjust target and stop loss based on optimal entry
                    price_ratio = optimal_entry / signal_info['entry_price']
                    signal_info['target_price'] *= price_ratio
                    signal_info['stop_loss'] *= price_ratio

                    # Calculate risk-reward ratio
                    if signal_type == 'LONG':
                        risk = optimal_entry - signal_info['stop_loss']
                        reward = signal_info['target_price'] - optimal_entry
                    else:  # SHORT
                        risk = signal_info['stop_loss'] - optimal_entry
                        reward = optimal_entry - signal_info['target_price']

                    signal_info['risk_reward_ratio'] = reward / risk if risk > 0 else 0

                    # Add to high probability signals
                    high_prob_signals.append((signal, signal_info))

            # Sort by win rate and risk-reward ratio
            high_prob_signals.sort(key=lambda x: (x[1]['win_rate'] * x[1]['risk_reward_ratio']), reverse=True)

        except Exception as e:
            print(f"Error in get_high_probability_signals: {e}")

        return high_prob_signals

    def analyze_coin(self, symbol):
        """Analyze a single coin and return its potential"""
        try:
            # Fetch data
            df = self.fetch_ohlcv(symbol, limit=100)
            if df is None or len(df) < 50:  # Need enough data for analysis
                return None

            # Calculate indicators
            df = self.calculate_indicators(df)

            # Generate signals
            signals, signal_details = self.generate_signals(df)

            # Get high probability signals
            high_prob_signals = self.get_high_probability_signals(df, signals, signal_details)

            if not high_prob_signals:
                return None

            # Get the best signal
            best_signal, best_info = high_prob_signals[0]

            # Calculate a score based on win rate, risk-reward ratio, and sample size
            score = best_info['win_rate'] * best_info['risk_reward_ratio'] * min(1, best_info['sample_size'] / 10)

            # Calculate volatility (as a positive factor for potential profit)
            volatility = df['high'].pct_change().abs().mean() * 100  # Average absolute % change

            # Calculate volume (higher volume means more liquidity)
            avg_volume = df['volume'].mean()

            # Calculate momentum (recent price direction)
            momentum = (df['close'].iloc[-1] / df['close'].iloc[-20] - 1) * 100  # % change over last 20 periods

            # Calculate a combined potential score
            potential_score = score * (1 + volatility/100) * (1 + momentum/100 if best_info['type'] == 'LONG' else 1 - momentum/100)

            return {
                'symbol': symbol,
                'score': potential_score,
                'win_rate': best_info['win_rate'],
                'risk_reward': best_info['risk_reward_ratio'],
                'signal_type': best_info['type'],
                'indicator': best_info['indicator'],
                'entry_price': best_info['optimal_entry'],
                'target_price': best_info['target_price'],
                'stop_loss': best_info['stop_loss'],
                'volatility': volatility,
                'momentum': momentum,
                'volume': avg_volume
            }
        except Exception as e:
            print(f"Error analyzing {symbol}: {e}")
            return None

    def scan_for_opportunities(self):
        """Scan all available coins for trading opportunities"""
        print("\n🔍 Đang quét thị trường để tìm các coin tiềm năng...")

        # Get all USDT trading pairs (defaulting to futures for Bybit, or general for others)
        pairs = self.get_usdt_pairs(exchange_type='future' if self.exchange.id == 'bybit' else 'spot')
        print(f"Tìm thấy {len(pairs)} cặp giao dịch USDT")

        # Analyze each coin in parallel
        results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            future_to_symbol = {executor.submit(self.analyze_coin, symbol): symbol for symbol in pairs}
            for future in concurrent.futures.as_completed(future_to_symbol):
                result = future.result()
                if result:
                    results.append(result)

        # Sort by potential score
        results.sort(key=lambda x: x['score'], reverse=True)

        # Update top coins
        self.top_coins = results[:self.top_n]

        # Update last scan time
        self.last_scan_time = time.time()

        return self.top_coins

    def display_opportunities(self, opportunities):
        """Display the trading opportunities"""
        if not opportunities:
            print("Không có coin nào đạt đủ điều kiện trong thời điểm này!")
            return

        print("\n🚀 CÁC CƠ HỘI GIAO DỊCH TIỀM NĂNG:")

        # Prepare table data
        table_data = []
        for coin in opportunities:
            current_price = coin['entry_price']
            profit_potential = abs((coin['target_price'] - current_price) / current_price * 100)
            risk = abs((coin['stop_loss'] - current_price) / current_price * 100)

            table_data.append([
                coin['symbol'],
                f"{coin['signal_type']} ({coin['indicator']})",
                f"{coin['win_rate']:.1f}%",
                f"{coin['risk_reward']:.2f}",
                f"{current_price:.4f}",
                f"{coin['target_price']:.4f}",
                f"{coin['stop_loss']:.4f}",
                f"{profit_potential:.2f}%",
                f"{risk:.2f}%"
            ])

        # Print table
        headers = ["Coin", "Tín hiệu", "Tỉ lệ thắng", "R/R", "Giá vào", "Mục tiêu", "Dừng lỗ", "Lợi nhuận", "Rủi ro"]
        print(tabulate(table_data, headers=headers, tablefmt="grid"))

        # Print detailed information for the top coin if in auto_scan mode
        if self.auto_scan and self.top_coins:
            top_coin = self.top_coins[0]
            print(f"\n💎 CƠ HỘI TỐT NHẤT: {top_coin['symbol']}")
            print(f"   Loại lệnh: {top_coin['signal_type']}")
            print(f"   Chỉ báo: {top_coin['indicator']}")
            print(f"   Tỉ lệ thắng: {top_coin['win_rate']:.1f}%")
            print(f"   Tỉ lệ lợi nhuận/rủi ro: {top_coin['risk_reward']:.2f}")
            print(f"   Giá vào lệnh tối ưu: {top_coin['entry_price']:.6f} USDT")
            print(f"   Giá mục tiêu: {top_coin['target_price']:.6f} USDT")
            print(f"   Giá dừng lỗ: {top_coin['stop_loss']:.6f} USDT")
            print(f"   Biến động: {top_coin['volatility']:.2f}%")
            print(f"   Xu hướng: {top_coin['momentum']:.2f}%")

    def run(self):
        """Main bot loop"""
        print(f"Starting trading bot on {self.exchange.id} for {self.timeframe} timeframe")
        if self.auto_scan:
            print(f"Auto scan for opportunities: Enabled (scanning every {self.scan_interval // 60} minutes)")
        else:
            print(f"Analyzing specific symbol: {self.specific_symbol}")

        # Initial scan if auto scan is enabled
        if self.auto_scan:
            self.scan_for_opportunities()

        while True:
            try:
                current_time = time.time()

                # Check if it's time to scan for new opportunities (only in auto_scan mode)
                if self.auto_scan and (current_time - self.last_scan_time) > self.scan_interval:
                    print("\nĐã đến thời gian quét thị trường định kỳ...")
                    self.scan_for_opportunities()
                    self.display_opportunities(self.top_coins)
                elif not self.auto_scan and self.specific_symbol:
                    # Analyze and display for specific symbol
                    print(f"\nTime: {datetime.now()}")
                    print(f"Analyzing {self.specific_symbol}...")
                    result = self.analyze_coin(self.specific_symbol)
                    if result:
                        self.display_opportunities([result])
                    else:
                        print(f"Không có tín hiệu nào cho {self.specific_symbol} tại thời điểm này.")

                print(f"\nCập nhật tiếp theo sau {self.scan_interval // 60} phút...")
                time.sleep(self.scan_interval)

            except Exception as e:
                print(f"An error occurred in main loop: {e}")
                time.sleep(60) # Wait a bit before retrying

if __name__ == '__main__':
    # Scan top 10 coins with highest win rate on Binance every hour
    print("🚀 Khởi động bot phân tích 10 coin có tỉ lệ thắng cao nhất!")
    print("📊 Sàn giao dịch: Binance")
    print("⏰ Khung thời gian: 1 giờ")
    print("🔄 Cập nhật mỗi: 60 phút")
    print("🎯 Số coin phân tích: Top 10")
    print("-" * 50)

    bot = CryptoTradingBot(
        exchange_id='binance',
        timeframe='1h',
        scan_interval=3600,
        top_n=10,
        auto_scan=True
    )
    bot.run()
